## Backend Repo for the [Electronic Asset Management System (EAMS) – Prototype MVP](https://spheres.crystal.ice4po.co.za/internal-projects/intranet-services/development-services/prototype_eams_mvp)

---

## Overview
The EAMS MVP is a **3-week prototype** designed to demonstrate core asset management functionality for real-time tracking, depreciation automation, barcode-based asset verification, and reporting readiness. It aims to provide a **lightweight, scalable foundation** for future system expansion.

---

## Objectives
- Track physical assets in real-time
- Automate asset depreciation and WIP management
- Enable barcode verification (via mobile)
- Provide audit-ready fixed asset reporting

---

## Key Features (MVP Scope)
- ✅ **Asset Registry Module**
- ✅ **Barcode Scanning & Upload (iOS/Android browser compatible)**
- ✅ **Depreciation Engine**
- ✅ **Work-in-Progress Tracker**
- ✅ **Role-Based Authentication**
- ✅ **Audit Trail & Reporting Dashboard**

> Out of Scope: ERP integration, predictive analytics, full native mobile app

---

## Milestones (3-Week Prototype Timeline)
| Week | Milestone                                      |
|------|------------------------------------------------|
| 1    | Kickoff, Asset Registry, Barcode Scanning      |
| 2    | Depreciation, WIP Tracker, Internal Feedback   |
| 3    | UAT, Training, Go-Live, Support Readiness      |

---

## Team Roles
| Role            | Members                                 |
|-----------------|------------------------------------------|
|**Maintainer**<br>Teboho L. Mphuthi                | Overall project oversight, milestone validation, governance alignment. | Executive briefs, risk escalations, milestone reviews.        |
| **Backend Devs**<br>Nathi Ngozo, Mohlatlego Sekhitla | Backend development, module implementation.                            | Task-level updates, standups, milestone reviews.              |
| **Frontend Dev**<br>Amukelani Ngobene           | UI/UX and frontend module development.                                 | Progress updates, UI alignment, and standups.                     |
| **Business Analyst**<br>Rethabile Mochali       | Requirements documentation, test case definition.                      | Requirement clarifications, risk logs, and documentation updates. |
| **QA/Support**<br>Vusumuzi Tswai                | Testing, issue resolution, support deployment.                         | Test results, issue escalation, defect logs.                  |
---

## Access & Authentication
- Role-based access control (Admin, Manager, Viewer)
- Login via secure user credentials

---

## Testing & Feedback
- Internal testing (Week 2)
- UAT by finance and asset teams (Week 3)
- Feedback collected via GitLab issues or survey form

---

## Repository Structure
```
/docs                → Project documentation
/frontend            → UI code (registry, dashboard, scanning)
/backend             → Services (depreciation, WIP)
/scripts             → Automation & deployment scripts
/tests               → Test cases and UAT checklists
.gitlab-ci.yml       → CI/CD Pipeline config
README.md            → Project overview
```

---

## Success Criteria
- MVP delivered on time with working core modules
- Positive feedback from UAT participants
- System ready for audit prep and asset roll-out planning

---

## 📬 Contact
- **Project Manager:** [Teboho L. Mphuthi] – [<EMAIL>]  
- **Sponsor Contact:** Business Development Office – [<EMAIL>]



### Backend Setup
1. Navigate to the backend directory:
```bash
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create environment file:
```bash
cp .env.example .env
```

5. Update `.env` with your MongoDB Atlas connection string and other settings

6. Start the FastAPI server:
```bash
uvicorn main:app --reload
```

The API will be available at `http://localhost:8000`
API documentation: `http://localhost:8000/docs`
